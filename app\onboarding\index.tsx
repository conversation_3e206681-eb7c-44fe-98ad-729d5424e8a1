import React, { useState, useRef } from 'react';
import { 
	View, 
	Text, 
	Pressable, 
	SafeAreaView, 
	Dimensions, 
	FlatList, 
	NativeSyntheticEvent, 
	NativeScrollEvent 
} from 'react-native';
import { Image } from 'expo-image';
import { router } from 'expo-router';

const { width } = Dimensions.get('window');

interface OnboardingSlide {
	id: string;
	image: any;
	title: string;
	description: string;
}

const onboardingData: OnboardingSlide[] = [
	{
		id: '1',
		image: require('@/assets/images/slideOneImg.png'),
		title: 'Welcome to Camell',
		description: 'Discover amazing features and get started with your journey.',
	},
	{
		id: '2',
		image: require('@/assets/images/slideTwoImg.png'),
		title: 'Easy to Use',
		description: 'Simple and intuitive interface designed for everyone.',
	},
	{
		id: '3',
		image: require('@/assets/images/slideThreeImg.png'),
		title: 'Get Started',
		description: 'Join thousands of users and start your experience today.',
	},
];

export default function OnboardingScreen() {
	const [currentIndex, setCurrentIndex] = useState(0);
	const flatListRef = useRef<FlatList>(null);

	const handleScroll = (event: NativeSyntheticEvent<NativeScrollEvent>) => {
		const contentOffsetX = event.nativeEvent.contentOffset.x;
		const index = Math.round(contentOffsetX / width);
		setCurrentIndex(index);
	};

	const goToNext = () => {
		if (currentIndex < onboardingData.length - 1) {
			flatListRef.current?.scrollToIndex({ index: currentIndex + 1 });
		} else {
			router.replace('/auth/login');
		}
	};

	const goToPrevious = () => {
		if (currentIndex > 0) {
			flatListRef.current?.scrollToIndex({ index: currentIndex - 1 });
		}
	};

	const skipOnboarding = () => {
		router.replace('/auth/login');
	};

	const renderSlide = ({ item }: { item: OnboardingSlide }) => (
		<View className="flex-1 items-center justify-center px-6" style={{ width }}>
			<Image
				source={item.image}
				className="w-80 h-80 mb-8"
				contentFit="contain"
			/>
			<Text className="text-3xl font-bold text-secondary text-center mb-4">
				{item.title}
			</Text>
			<Text className="text-grey-50 text-center text-base leading-6 px-4">
				{item.description}
			</Text>
		</View>
	);

	const renderPagination = () => (
		<View className="flex-row justify-center items-center mb-8">
			{onboardingData.map((_, index) => (
				<View
					key={index}
					className={`h-2 rounded-full mx-1 ${
						index === currentIndex 
							? 'bg-primary w-8' 
							: 'bg-grey-30 w-2'
					}`}
				/>
			))}
		</View>
	);

	return (
		<SafeAreaView className="flex-1 bg-bg-color">
			{/* Skip Button */}
			<View className="flex-row justify-end px-6 pt-4">
				<Pressable onPress={skipOnboarding}>
					<Text className="text-grey-50 font-medium">Skip</Text>
				</Pressable>
			</View>

			{/* Slides */}
			<FlatList
				ref={flatListRef}
				data={onboardingData}
				renderItem={renderSlide}
				horizontal
				pagingEnabled
				showsHorizontalScrollIndicator={false}
				onScroll={handleScroll}
				scrollEventThrottle={16}
				keyExtractor={(item) => item.id}
			/>

			{/* Pagination */}
			{renderPagination()}

			{/* Navigation Buttons */}
			<View className="flex-row justify-between items-center px-6 pb-8">
				{currentIndex > 0 ? (
					<Pressable 
						onPress={goToPrevious}
						className="bg-grey-20 px-6 py-3 rounded-lg"
					>
						<Text className="text-secondary font-medium">Previous</Text>
					</Pressable>
				) : (
					<View />
				)}

				<Pressable 
					onPress={goToNext}
					className="bg-primary px-8 py-3 rounded-lg"
				>
					<Text className="text-white font-semibold">
						{currentIndex === onboardingData.length - 1 ? 'Get Started' : 'Next'}
					</Text>
				</Pressable>
			</View>
		</SafeAreaView>
	);
}
