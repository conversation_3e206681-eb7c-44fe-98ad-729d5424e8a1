import { Link } from 'expo-router';
import React, { useState } from 'react';
import {
	KeyboardAvoidingView,
	Platform,
	Pressable,
	SafeAreaView,
	Text,
	TextInput,
	View,
} from 'react-native';

const Login = () => {
	const [email, setEmail] = useState('');
	const [password, setPassword] = useState('');

	const handleLogin = () => {
		// TODO: Implement login logic
		console.log('Login pressed', { email, password });
	};

	return (
		<SafeAreaView className='flex-1 bg-bg-color'>
			<KeyboardAvoidingView
				className='flex-1'
				behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
				<View className='flex-1 px-6 justify-center'>
					{/* Header */}
					<View className='mb-8'>
						<Text className='text-3xl font-bold text-secondary mb-2'>
							Welcome Back
						</Text>
						<Text className='text-grey-50 text-base'>
							Sign in to your account
						</Text>
					</View>

					{/* Form */}
					<View className='mb-6'>
						{/* Email Input */}
						<View className='mb-4'>
							<Text className='text-secondary font-medium mb-2'>Email</Text>
							<TextInput
								className='bg-white border border-grey-30 rounded-lg px-4 py-3 text-secondary'
								placeholder='Enter your email'
								placeholderTextColor='#828282'
								value={email}
								onChangeText={setEmail}
								keyboardType='email-address'
								autoCapitalize='none'
							/>
						</View>

						{/* Password Input */}
						<View className='mb-6'>
							<Text className='text-secondary font-medium mb-2'>Password</Text>
							<TextInput
								className='bg-white border border-grey-30 rounded-lg px-4 py-3 text-secondary'
								placeholder='Enter your password'
								placeholderTextColor='#828282'
								value={password}
								onChangeText={setPassword}
								secureTextEntry
							/>
						</View>

						{/* Login Button */}
						<Pressable
							className='bg-primary py-4 rounded-lg items-center mb-4'
							onPress={handleLogin}>
							<Text className='text-white font-semibold text-base'>
								Sign In
							</Text>
						</Pressable>

						{/* Forgot Password */}
						<Pressable className='items-center mb-6'>
							<Text className='text-primary font-medium'>Forgot Password?</Text>
						</Pressable>
					</View>

					{/* Sign Up Link */}
					<View className='items-center'>
						<Text className='text-grey-50 mb-2'>Don't have an account?</Text>
						<Link
							href='/auth/register'
							asChild>
							<Pressable>
								<Text className='text-primary font-semibold'>
									Create Account
								</Text>
							</Pressable>
						</Link>
					</View>
				</View>
			</KeyboardAvoidingView>
		</SafeAreaView>
	);
};

export default Login;
