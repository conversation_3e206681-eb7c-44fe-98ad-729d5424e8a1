import {
	DarkTheme,
	De<PERSON><PERSON><PERSON>hem<PERSON>,
	Theme<PERSON>rovider,
} from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import 'react-native-reanimated';
import './global.css';

import { useColorScheme } from '@/hooks/useColorScheme';
import { ApiAuthSync } from '@/providers/api-auth';
import { ReactQueryProvider } from '@/providers/react-query';

function RootLayoutNav() {
	// TODO: Replace with actual state management (AsyncStorage, Zustand, etc.)
	const isFirstTime = true; // Check if user has seen onboarding
	const isAuthenticated = false; // Check if user is logged in

	return (
		<Stack screenOptions={{ headerShown: false }}>
			{isAuthenticated ? (
				<Stack.Screen
					name='(tabs)'
					options={{ headerShown: false }}
				/>
			) : isFirstTime ? (
				<Stack.Screen
					name='onboarding'
					options={{ headerShown: false }}
				/>
			) : (
				<Stack.Screen
					name='auth'
					options={{ headerShown: false }}
				/>
			)}
		</Stack>
	);
}

export default function RootLayout() {
	const colorScheme = useColorScheme();
	const [loaded] = useFonts({
		SpaceMono: require('../assets/fonts/Inter-VariableFont_opsz,wght.ttf'),
	});

	if (!loaded) {
		// Async font loading only occurs in development.
		return null;
	}

	return (
		<ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
			<ReactQueryProvider>
				<ApiAuthSync />
				<RootLayoutNav />
				<StatusBar
					style='light'
					backgroundColor='#F74323'
				/>
			</ReactQueryProvider>
		</ThemeProvider>
	);
}
