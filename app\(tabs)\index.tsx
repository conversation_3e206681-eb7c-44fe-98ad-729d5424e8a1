import React from 'react';
import { Pressable, SafeAreaView, Text, View } from 'react-native';

export default function HomeScreen() {
	return (
		<SafeAreaView className='flex-1 bg-bg-color'>
			<View className='flex-1 px-6 py-8'>
				{/* Header */}
				<View className='mb-8'>
					<Text className='text-2xl font-bold text-secondary mb-2'>
						Welcome to Camell! 🎉
					</Text>
					<Text className='text-grey-50 text-base'>
						Your app is ready to go with your custom theme
					</Text>
				</View>

				{/* Content */}
				<View className='flex-1 justify-center items-center'>
					<View className='bg-light-orange p-8 rounded-2xl border border-grey-30 mb-6'>
						<Text className='text-primary text-6xl text-center mb-4'>🚀</Text>
						<Text className='text-secondary font-semibold text-lg text-center mb-2'>
							Setup Complete!
						</Text>
						<Text className='text-grey-50 text-center'>
							Your app is configured with NativeWind, custom colors, and ready
							for development.
						</Text>
					</View>

					{/* Color Showcase */}
					<View className='w-full mb-6'>
						<Text className='text-secondary font-semibold mb-3'>
							Your Color Palette:
						</Text>
						<View className='flex-row flex-wrap justify-between'>
							<View className='bg-primary w-16 h-16 rounded-lg mb-2' />
							<View className='bg-secondary w-16 h-16 rounded-lg mb-2' />
							<View className='bg-light-orange w-16 h-16 rounded-lg mb-2 border border-grey-30' />
							<View className='bg-grey-50 w-16 h-16 rounded-lg mb-2' />
						</View>
					</View>

					{/* Action Button */}
					<Pressable className='bg-primary px-8 py-4 rounded-lg w-full'>
						<Text className='text-white font-semibold text-center text-base'>
							Start Building Your App
						</Text>
					</Pressable>
				</View>
			</View>
		</SafeAreaView>
	);
}
