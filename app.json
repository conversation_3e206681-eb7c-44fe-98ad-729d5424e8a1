{"expo": {"name": "Camell", "slug": "camell", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "camell", "userInterfaceStyle": "automatic", "newArchEnabled": true, "primaryColor": "#F74323", "backgroundColor": "#FEFEFE", "splash": {"image": "./assets/images/splashScreen.png", "resizeMode": "contain", "backgroundColor": "#F74323"}, "ios": {"supportsTablet": true, "backgroundColor": "#FEFEFE", "userInterfaceStyle": "automatic"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/icon.png", "backgroundColor": "#F74323"}, "edgeToEdgeEnabled": true, "backgroundColor": "#FEFEFE", "statusBarBackgroundColor": "#F74323", "navigationBarColor": "#FEFEFE"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/icon.png", "themeColor": "#F74323"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splashScreen.png", "imageWidth": 300, "resizeMode": "contain", "backgroundColor": "#F74323"}]], "experiments": {"typedRoutes": true}}}