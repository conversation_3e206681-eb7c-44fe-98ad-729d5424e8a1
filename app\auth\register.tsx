import { Link } from 'expo-router';
import React, { useState } from 'react';
import {
	KeyboardAvoidingView,
	Platform,
	Pressable,
	SafeAreaView,
	ScrollView,
	Text,
	TextInput,
	View,
} from 'react-native';

const Register = () => {
	const [name, setName] = useState('');
	const [email, setEmail] = useState('');
	const [password, setPassword] = useState('');
	const [confirmPassword, setConfirmPassword] = useState('');

	const handleRegister = () => {
		// TODO: Implement registration logic
		console.log('Register pressed', { name, email, password, confirmPassword });
	};

	return (
		<SafeAreaView className='flex-1 bg-bg-color'>
			<KeyboardAvoidingView
				className='flex-1'
				behavior={Platform.OS === 'ios' ? 'padding' : 'height'}>
				<ScrollView
					className='flex-1 px-6'
					showsVerticalScrollIndicator={false}>
					<View className='flex-1 justify-center py-8'>
						{/* Header */}
						<View className='mb-8'>
							<Text className='text-3xl font-bold text-secondary mb-2'>
								Create Account
							</Text>
							<Text className='text-grey-50 text-base'>
								Sign up to get started
							</Text>
						</View>

						{/* Form */}
						<View className='mb-6'>
							{/* Name Input */}
							<View className='mb-4'>
								<Text className='text-secondary font-medium mb-2'>
									Full Name
								</Text>
								<TextInput
									className='bg-white border border-grey-30 rounded-lg px-4 py-3 text-secondary'
									placeholder='Enter your full name'
									placeholderTextColor='#828282'
									value={name}
									onChangeText={setName}
									autoCapitalize='words'
								/>
							</View>

							{/* Email Input */}
							<View className='mb-4'>
								<Text className='text-secondary font-medium mb-2'>Email</Text>
								<TextInput
									className='bg-white border border-grey-30 rounded-lg px-4 py-3 text-secondary'
									placeholder='Enter your email'
									placeholderTextColor='#828282'
									value={email}
									onChangeText={setEmail}
									keyboardType='email-address'
									autoCapitalize='none'
								/>
							</View>

							{/* Password Input */}
							<View className='mb-4'>
								<Text className='text-secondary font-medium mb-2'>
									Password
								</Text>
								<TextInput
									className='bg-white border border-grey-30 rounded-lg px-4 py-3 text-secondary'
									placeholder='Create a password'
									placeholderTextColor='#828282'
									value={password}
									onChangeText={setPassword}
									secureTextEntry
								/>
							</View>

							{/* Confirm Password Input */}
							<View className='mb-6'>
								<Text className='text-secondary font-medium mb-2'>
									Confirm Password
								</Text>
								<TextInput
									className='bg-white border border-grey-30 rounded-lg px-4 py-3 text-secondary'
									placeholder='Confirm your password'
									placeholderTextColor='#828282'
									value={confirmPassword}
									onChangeText={setConfirmPassword}
									secureTextEntry
								/>
							</View>

							{/* Register Button */}
							<Pressable
								className='bg-primary py-4 rounded-lg items-center mb-4'
								onPress={handleRegister}>
								<Text className='text-white font-semibold text-base'>
									Create Account
								</Text>
							</Pressable>
						</View>

						{/* Sign In Link */}
						<View className='items-center'>
							<Text className='text-grey-50 mb-2'>
								Already have an account?
							</Text>
							<Link
								href='/auth/login'
								asChild>
								<Pressable>
									<Text className='text-primary font-semibold'>Sign In</Text>
								</Pressable>
							</Link>
						</View>
					</View>
				</ScrollView>
			</KeyboardAvoidingView>
		</SafeAreaView>
	);
};

export default Register;
